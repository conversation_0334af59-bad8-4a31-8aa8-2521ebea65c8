# Sample Files for Testing

This directory contains sample files for testing the PDC Vulnerability Checker tool.
The tool currently relies on pnpm version 8.15.6. Updating to a newer version of pnpm might break the tool and require it to be adapted.

## Files

- **`package.json`** - Contains intentionally vulnerable packages for testing
- **`pnpm-lock.yaml`** - Lock file generated from the package.json (for testing)
- **`vulnerability-allowlist.json`** - Example allowlist file with approved vulnerabilities
- **`vulnerability-allowlist-invalid.json`** - Invalid allowlist file for testing error handling
- **`AuditOutput.json`** - Sample pnpm audit output for reference

## Testing the Tool

1. **Install vulnerable dependencies:**

   ```bash
   cd samples
   pnpm install --lockfile-only
   ```

2. **Run the vulnerability checker:**

   ```bash
   # Check all dependencies (default)
   node ../dist/check-vulnerabilities.js

   # Check only production dependencies
   node ../dist/check-vulnerabilities.js --prod-only
   ```

The sample `package.json` includes packages with known high/critical vulnerabilities:

- `lodash@4.17.15` (production)
- `axios@0.21.0` (dev)
- `express-fileupload@1.1.8` (dev)
- `moment@2.29.0` (dev)

This will generate vulnerabilities that can be tested with the allowlist functionality.
