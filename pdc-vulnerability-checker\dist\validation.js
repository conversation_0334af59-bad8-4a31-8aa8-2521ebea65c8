"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.parseAndValidateAuditOutput = exports.parseAndValidateAllowlist = exports.validateAuditOutput = exports.validateAllowlist = exports.PnpmAuditOutputSchema = exports.PnpmAuditMetadataSchema = exports.PnpmAuditVulnerabilitiesSchema = exports.PnpmAuditActionSchema = exports.PnpmAuditActionResolveSchema = exports.PnpmAuditAdvisorySchema = exports.PnpmAuditFindingSchema = exports.PnpmAuditSeveritySchema = exports.AllowlistSchema = exports.AllowlistEntrySchema = void 0;
const zod_1 = require("zod");
const utils_1 = require("./utils");
// Zod schema for AllowlistEntry
exports.AllowlistEntrySchema = zod_1.z.object({
    approvedBy: zod_1.z.string().min(3, "approvedBy must not be empty"),
    reason: zod_1.z.string().min(20, "reason must not be empty"),
    date: zod_1.z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "date must be in YYYY-MM-DD format"),
});
// Zod schema for Allowlist
exports.AllowlistSchema = zod_1.z.record(zod_1.z.string(), exports.AllowlistEntrySchema);
// Zod schema for audit output severity levels
exports.PnpmAuditSeveritySchema = zod_1.z.enum(["critical", "high", "moderate", "low"]);
// Zod schema for audit advisory findings
exports.PnpmAuditFindingSchema = zod_1.z.object({
    version: zod_1.z.string(),
    paths: zod_1.z.array(zod_1.z.string()),
});
// Zod schema for audit advisory
exports.PnpmAuditAdvisorySchema = zod_1.z.object({
    id: zod_1.z.number(),
    severity: exports.PnpmAuditSeveritySchema,
    module_name: zod_1.z.string(),
    title: zod_1.z.string(),
    overview: zod_1.z.string(),
    updated: zod_1.z.string(),
    github_advisory_id: zod_1.z.string(),
    cves: zod_1.z.array(zod_1.z.string()),
    findings: zod_1.z.array(exports.PnpmAuditFindingSchema),
    recommendation: zod_1.z.string(),
    patched_versions: zod_1.z.string(),
    vulnerable_versions: zod_1.z.string(),
});
// Zod schema for action resolve items
exports.PnpmAuditActionResolveSchema = zod_1.z.object({
    id: zod_1.z.number(),
    path: zod_1.z.string(),
    dev: zod_1.z.boolean(),
    optional: zod_1.z.boolean(),
    bundled: zod_1.z.boolean(),
});
// Zod schema for audit actions
exports.PnpmAuditActionSchema = zod_1.z.object({
    action: zod_1.z.enum(["update", "review", "install"]), // Common action types
    module: zod_1.z.string(),
    resolves: zod_1.z.array(exports.PnpmAuditActionResolveSchema),
    target: zod_1.z.string().optional(), // Only present for "update" actions
    depth: zod_1.z.number().optional(), // Only present for "update" actions
});
// Zod schema for audit metadata vulnerabilities
exports.PnpmAuditVulnerabilitiesSchema = zod_1.z.object({
    info: zod_1.z.number(),
    low: zod_1.z.number(),
    moderate: zod_1.z.number(),
    high: zod_1.z.number(),
    critical: zod_1.z.number(),
});
// Zod schema for audit metadata
exports.PnpmAuditMetadataSchema = zod_1.z.object({
    vulnerabilities: exports.PnpmAuditVulnerabilitiesSchema,
    dependencies: zod_1.z.number(),
    devDependencies: zod_1.z.number(),
    optionalDependencies: zod_1.z.number(),
    totalDependencies: zod_1.z.number(),
});
// Zod schema for complete audit output
exports.PnpmAuditOutputSchema = zod_1.z.object({
    advisories: zod_1.z.record(zod_1.z.string(), exports.PnpmAuditAdvisorySchema),
    actions: zod_1.z.array(exports.PnpmAuditActionSchema),
    metadata: exports.PnpmAuditMetadataSchema,
    // Currently no sample data, so using z.unknown() for now:
    muted: zod_1.z.array(zod_1.z.unknown()).optional(),
});
// Type guards using Zod schemas
// export const isAllowlistEntry = (value: unknown): value is AllowlistEntry => {
//   return AllowlistEntrySchema.safeParse(value).success;
// };
// export const isAllowlist = (value: unknown): value is Allowlist => {
//   return AllowlistSchema.safeParse(value).success;
// };
// Validation functions that throw on error
// export const validateAllowlistEntry = (value: unknown): AllowlistEntry => {
//   return AllowlistEntrySchema.parse(value);
// };
const validateAllowlist = (value) => {
    return exports.AllowlistSchema.parse(value);
};
exports.validateAllowlist = validateAllowlist;
// Validation function for audit output
const validateAuditOutput = (value) => {
    return exports.PnpmAuditOutputSchema.parse(value);
};
exports.validateAuditOutput = validateAuditOutput;
// Utility function to validate allowlist from JSON string
const parseAndValidateAllowlist = (jsonString) => {
    try {
        const parsed = JSON.parse(jsonString);
        return (0, exports.validateAllowlist)(parsed);
    }
    catch (error) {
        if (error instanceof zod_1.z.ZodError) {
            const errorMessages = error.issues.map((issue) => `${issue.path.join(".")}: ${issue.message}`).join(", ");
            throw new Error(`Invalid allowlist format: ${errorMessages}`);
        }
        throw new Error(`Failed to parse allowlist JSON: ${(0, utils_1.getErrorMsg)(error)}`);
    }
};
exports.parseAndValidateAllowlist = parseAndValidateAllowlist;
// Utility function to validate audit output from JSON string
const parseAndValidateAuditOutput = (jsonString) => {
    try {
        const parsed = JSON.parse(jsonString);
        return (0, exports.validateAuditOutput)(parsed);
    }
    catch (error) {
        if (error instanceof zod_1.z.ZodError) {
            const errorMessages = error.issues.map((issue) => `${issue.path.join(".")}: ${issue.message}`).join(", ");
            throw new Error(`Invalid audit output format: ${errorMessages}`);
        }
        throw new Error(`Failed to parse audit output JSON: ${(0, utils_1.getErrorMsg)(error)}`);
    }
};
exports.parseAndValidateAuditOutput = parseAndValidateAuditOutput;
