# 🔒 PDC Vulnerability Checker

This tool validates security vulnerabilities in `pnpm`-based projects. It checks **production dependencies by default** and supports allowlisting for approved vulnerabilities.

The tool currently relies on pnpm version 8.15.6. Updating to a newer version of pnpm might break the tool and require it to be adapted.

---

## 🚀 Features

- ✅ Checks production dependencies by default (use `--include-dev` for dev dependencies)
- ✅ Validates critical and high severity vulnerabilities only
- ✅ Supports vulnerability allowlisting with expiration dates
- ✅ Fails the pipeline if unapproved vulnerabilities are detected
- ✅ Built-in help with `--help` flag

---

## 🧪 Usage

### Local Development

```bash
# Check production dependencies only (default)
pnpm check

# Check all dependencies (production + development)
pnpm check:all

# Or run directly
node dist/check-vulnerabilities.js
node dist/check-vulnerabilities.js --include-dev
```

### Pipeline Usage

```yaml
- script: pnpm dlx @pdc_frontend/pdc-vulnerability-checker
  displayName: Check vulnerabilities
```

---

## 📁 Allowlist Format

Add a `vulnerability-allowlist.json` file in the root of your project to suppress known/approved vulnerabilities.

### ✅ Example

```json
{
  // Advisory ID as entry key
  "GHSA-vvpx-jr67-3p9c": {
    "approvedBy": "john.doe", // Approver: Person or team approving the allowlist entry
    "reason": "False positive - not exploitable in our use case", // Reason: Justification for the allowlist entry
    "date": "2024-01-15" // Creation date: Date the entry was created
  },
  "GHSA-h4hr-7fg3-h9x9": {
    "approvedBy": "security.team",
    "reason": "Temporary allowlist - fix scheduled for next sprint",
    "date": "2024-01-20"
  }
}
```

> **Note**: Consider implementing that entries expire after a period, e.g. 6 months.

---

## 📌 Exit Codes

- `0` — No unapproved critical/high severity vulnerabilities found
- `1` — Unapproved vulnerabilities detected or validation errors

---

## 🛠️ Command Line Options

```
Usage: check-vulnerabilities [options]

Options:
  --include-dev    Include development dependencies in vulnerability check
  --help, -h       Show this help message

By default, only production dependencies are checked for vulnerabilities.
```
