# 🔒 PDC Vulnerability Checker

This tool validates security vulnerabilities in `pnpm`-based projects. It checks **all dependencies by default** and supports allowlisting for approved vulnerabilities.

The tool currently relies on pnpm version 8.15.6. Updating to a newer version of pnpm might break the tool and require it to be adapted.

---

## 🚀 Features

- ✅ Checks all dependencies by default (use `--prod-only` or `-P` for production only)
- ✅ Validates critical and high severity vulnerabilities only
- ✅ Supports vulnerability allowlisting with expiration dates
- ✅ Fails the pipeline if unapproved vulnerabilities are detected
- ✅ Built-in help with `--help` flag

---

## 🧪 Usage

### Local Development

```bash
# Check all dependencies (default)
pnpm check

# Check production dependencies only
pnpm check:prod

# Or run directly
node dist/check-vulnerabilities.js
node dist/check-vulnerabilities.js --prod-only
node dist/check-vulnerabilities.js -P
```

### Pipeline Usage

```yaml
- script: pnpm dlx @pdc_frontend/pdc-vulnerability-checker
  displayName: Check vulnerabilities
```

---

## 📁 Allowlist Format

Add a `vulnerability-allowlist.json` file in the root of your project to suppress known/approved vulnerabilities. You can specify a custom path using `--allowlist-path=PATH` or `-A PATH`.

### ✅ Example

```json
{
  // Advisory ID as entry key
  "GHSA-vvpx-jr67-3p9c": {
    "approvedBy": "john.doe", // Approver: Person or team approving the allowlist entry
    "reason": "False positive - not exploitable in our use case", // Reason: Justification for the allowlist entry
    "date": "2024-01-15" // Creation date: Date the entry was created
  },
  "GHSA-h4hr-7fg3-h9x9": {
    "approvedBy": "security.team",
    "reason": "Temporary allowlist - fix scheduled for next sprint",
    "date": "2024-01-20"
  }
}
```

> **Note**: Consider implementing that entries expire after a period, e.g. 6 months.

---

## 📌 Exit Codes

- `0` — No unapproved critical/high severity vulnerabilities found
- `1` — Unapproved vulnerabilities detected or validation errors

---

## 🛠️ Command Line Options

```
Usage: check-vulnerabilities [options]

Options:
  --prod-only, -P                 Check only production dependencies for vulnerabilities
  --allowlist-path=PATH, -A PATH  Specify path to allowlist file (default: ./vulnerability-allowlist.json)
  --help, -h                      Show this help message

By default, both production and development dependencies are checked for vulnerabilities.
```

### Examples

```bash
# Check all dependencies (default behavior)
node dist/check-vulnerabilities.js

# Check only production dependencies
node dist/check-vulnerabilities.js --prod-only
node dist/check-vulnerabilities.js -P

# Use custom allowlist file
node dist/check-vulnerabilities.js --allowlist-path=custom-allowlist.json
node dist/check-vulnerabilities.js -A custom-allowlist.json

# Combine options
node dist/check-vulnerabilities.js -P -A custom-allowlist.json
```
