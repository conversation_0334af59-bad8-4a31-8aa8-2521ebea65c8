import chalk from "chalk";
import { execSync } from "child_process";
import Table from "cli-table3";
import fs from "fs";
import path from "path";
import type { PackageJson } from "types-package-json";
import { Allowlist, PnpmAuditAdvisory, PnpmAuditOutput } from "./types";
import {
  brightRed,
  brightYellow,
  exitWithErrorLog,
  getAllowlistPath,
  getErrorMsg,
  logHelp2Console,
  severity2colorString,
} from "./utils";
import { parseAndValidateAllowlist, parseAndValidateAuditOutput } from "./validation";

// Parse command line arguments
const args = process.argv.slice(2);

// Show help if requested
if (args.includes("--help") || args.includes("-h")) {
  logHelp2Console();
  process.exit(0);
}

const prodOnly = args.includes("--prod-only") || args.includes("-P");

// Constants
const scriptDir = path.dirname(__filename);
const AUDIT_COMMAND = prodOnly ? "pnpm audit --json --prod" : "pnpm audit --json";
const allowlistPath = getAllowlistPath(args);

/** Log package name and version from package.json */
const logPackageInfo = (): void => {
  try {
    // Get the directory where this script is located, then go up one level to find package.json
    const packageJsonPath = path.join(scriptDir, "..", "package.json");
    const packageContent = fs.readFileSync(packageJsonPath, "utf-8");
    const packageData = JSON.parse(packageContent) as PackageJson;
    console.log(`📦 Package: ${packageData.name}, version: ${packageData.version}\n`);
  } catch (error) {
    exitWithErrorLog(`❌ Could not load package.json: ${getErrorMsg(error)}`);
  }
};

/** Load the allowlist from file. Return the content as string; if no file, return null. */
const loadAllowlist = (): string | null => {
  try {
    const content = fs.readFileSync(allowlistPath, "utf-8");
    console.log(`Allowlist read from: ${allowlistPath}\n`);
    return content;
  } catch (error: unknown) {
    // If the file exists but reading it fails, exit the script:
    if (error && typeof error === "object" && "code" in error && error.code !== "ENOENT") {
      exitWithErrorLog(`❌ ${getErrorMsg(error)}`);
    }
    // If the file doesn't exist, it's not an error, just continue without it:
    console.log(chalk.hex(brightYellow)("⚠️ No allowlist file found. Continuing without allowlist.\n"));
  }
  return null;
};

/** Parse and validate the allowlist file content. */
const checkAllowlistFile = (fileContent: string): Allowlist => {
  try {
    return parseAndValidateAllowlist(fileContent);
  } catch (error) {
    exitWithErrorLog(`❌ ${getErrorMsg(error)}`);
    // TypeScript doesn't understand that exitWithErrorLog never returns:
    throw new Error("unreachable");
  }
};

/** Run pnpm audit command, validate and parse the result */
const runAudit = (): PnpmAuditOutput => {
  try {
    const raw = execSync(AUDIT_COMMAND, { encoding: "utf-8" });
    const output: PnpmAuditOutput = parseAndValidateAuditOutput(raw);
    return output;
  } catch (error: unknown) {
    // 'pnpm audit' returns non-zero exit code when vulnerabilities are found
    // but still outputs valid JSON to stdout.
    // In that case, we parse the error.stdout and return that:
    if (error && typeof error === "object" && "stdout" in error && typeof error.stdout === "string") {
      try {
        const output: PnpmAuditOutput = parseAndValidateAuditOutput(error.stdout);
        return output;
      } catch (validationError) {
        exitWithErrorLog(`❌ ${getErrorMsg(validationError)}`);
      }
    }
    exitWithErrorLog(`❌ ${getErrorMsg(error)}`);
    // TypeScript doesn't understand that exitWithErrorLog never returns:
    throw new Error("unreachable");
  }
};

// Main audit check function
const checkVulnerabilities = (): void => {
  logPackageInfo();

  // Log what dependencies are being checked
  if (prodOnly) {
    console.log("🔍 Checking vulnerabilities in production dependencies only");
    console.log("   Use default (no flags) to include all dependencies\n");
  } else {
    console.log("🔍 Checking vulnerabilities in all dependencies\n");
  }

  /**
   * Three options for the allowlist file:
   * 1. It doesn't exist -> log that to the console and go on without the allowlist.
   * 2. It's invalid -> log that to the console and exit the script.
   * 3. It's valid -> return it and go on using the allowlist.
   */
  const allowlistFile = loadAllowlist();
  const allowlist = allowlistFile ? checkAllowlistFile(allowlistFile) : {};
  // Run the audit and parse the output (with Zod validation)
  const audit: PnpmAuditOutput = runAudit();

  // Extract all advisory objects from the audit report
  const allFindings: PnpmAuditAdvisory[] = Object.values(audit.advisories);

  // Filter for only critical or high severity issues
  const severeFindings = allFindings.filter(
    (advisory) => advisory.severity === "critical" || advisory.severity === "high",
  );

  // Further filter to only those not explicitly allowed in the allowlist
  // Use 'github_advisory_id' as the key for allowlist matching
  const unapproved = severeFindings.filter((adv) => !allowlist[adv.github_advisory_id]);
  const unApprNr = unapproved.length;

  // If unapproved issues are found, log them and mark CI failure
  if (unApprNr > 0) {
    console.log(chalk.hex(brightRed)(`🚨 Found ${unApprNr} unapproved critical/high severity vulnerabilities:`));
    // Go through each unapproved advisory and log its details:
    unapproved.forEach((adv) => {
      const severity = severity2colorString(adv.severity);
      const table = new Table({ colWidths: [20, 50] });
      table.push(["ID ", adv.github_advisory_id]);
      table.push(["Severity ", severity]);
      table.push(["Module ", adv.module_name]);
      table.push(["Updated ", adv.updated]);
      table.push(["Title ", adv.title]);
      table.push(["Recommendation ", adv.recommendation]);
      console.log(table.toString());
    });
    console.log(
      chalk.hex(brightYellow)(
        "📄 Tip: Add approved vulnerabilities to the allowlist with justification.\nRun 'pnpm audit' to get more details.",
      ),
    );
    // Return 1 to indicate failure (no need to exit here):
    process.exitCode = 1;
  } else {
    console.log(chalk.green("✅ No unapproved critical/high severity vulnerabilities found."));
    // Return 0 to indicate success:
    process.exitCode = 0;
  }
  console.log(""); // empty line
};

// Execute the audit check
checkVulnerabilities();
