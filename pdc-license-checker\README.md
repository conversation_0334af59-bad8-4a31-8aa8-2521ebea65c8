# 📄 PDC License Checker

This tool validates the licenses of all installed dependencies in a `pnpm`-based project. It is designed to run in **CI pipelines only** using `pnpm dlx`, with support for fine-grained and global license whitelisting.

---

## 🚀 Features

- ✅ Zero-install, run via `pnpm dlx` in your pipeline
- ✅ Validates both production (`--prod`) and development (`--dev`) dependencies
- ✅ Parses SPDX expressions like `MIT OR Apache-2.0`
- ✅ Classifies dependencies by risk and attribution requirement
- ✅ Supports flexible whitelisting: specific versions or all versions
- ✅ Fails the pipeline if unapproved licenses are detected

---

## 🧪 Pipeline Usage

No local installation is required. The tool runs with:

```yaml
- script: pnpm dlx @pdc_frontend/pdc-license-checker ./src
  displayName: Run license checks
```

Example from Azure DevOps:

```yaml
pnpm dlx @pdc_frontend/pdc-license-checker ./src
```

---

## 📁 Whitelist Format

Add a `license-whitelist.json` file in the root of your project to suppress known/approved packages.

### ✅ Example

```json
{
  "//": "Use 'package@version' to whitelist a specific version, or just 'package' to allow all versions.",
  "@pdcfrontendui/shared": "Internal package — license reviewed and accepted for all versions",
  "@pdcfrontendui/components@1.0.3": "blabla example with a specific version, its allowed because internal package by PDC",
  "caniuse-lite@1.0.30001713": "CC-BY-4.0 allowed in dev-only tooling"
}
```

### 🔧 Matching Behavior

| Entry              | Matches                                    |
| ------------------ | ------------------------------------------ |
| `"pkg-name@1.2.3"` | Whitelists only version 1.2.3              |
| `"pkg-name"`       | Whitelists **all versions** of the package |

Whitelisted packages are excluded from review/action warnings.

---

## 🧠 License Policy

The tool enforces four license categories:

| Dependency Type | License Category                   | Behavior                      |
| --------------- | ---------------------------------- | ----------------------------- |
| Runtime         | `NO_ACTION_RUNTIME_LICENSES`       | ✅ Allowed                    |
| Runtime         | `ACTION_REQUIRED_RUNTIME_LICENSES` | ⚠️ Attribution required       |
| Dev-only        | `NO_ACTION_DEV_LICENSES`           | ✅ Allowed                    |
| Dev-only        | `ACTION_REQUIRED_DEV_LICENSES`     | ⚠️ Attribution required       |
| Any             | Unclassified / Unknown             | ⚠️ Flagged unless whitelisted |

License expressions (e.g. `MIT OR Apache-2.0`) are parsed and matched if **any part** is in an allowed list.

---

## 📌 Exit Codes

- `0` — All licenses are approved or whitelisted
- `1` — At least one package needs attribution or review

---

## 🧰 License Reference

Use official SPDX identifiers for classification:  
🔗 https://spdx.org/licenses/
