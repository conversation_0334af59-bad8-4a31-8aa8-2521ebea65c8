{"name": "@pdc_frontend/pdc-license-checker", "version": "0.0.23", "description": "Enforces permissive license policy using pnpm", "packageManager": "pnpm@8.15.6", "bin": {"check-licenses": "./dist/check-licenses.js"}, "exports": {".": "./src/index.ts"}, "scripts": {"init": "pnpm i && pnpm run build", "build": "pnpm tsc", "release": "pnpm i && pnpm run build && pnpm publish --dry-run --no-git-checks && pnpm publish --no-git-checks", "bump:patch": "npm version patch", "bump:minor": "npm version minor", "bump:major": "npm version major"}, "files": ["dist"], "devDependencies": {"@types/node": "^24.0.3", "@types/spdx-expression-parse": "^3.0.5", "typescript": "^5.4.0"}, "publishConfig": {"registry": "https://svrtfs1/PDC/pdcTools/_packaging/pdc_frontend/npm/registry/"}, "dependencies": {"spdx-expression-parse": "^4.0.0"}}