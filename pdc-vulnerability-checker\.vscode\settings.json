{"eslint.useFlatConfig": true, "eslint.workingDirectories": [{"mode": "auto"}], "eslint.validate": ["javascript", "typescript"], "eslint.format.enable": false, "eslint.lintTask.enable": true, "typescript.preferences.includePackageJsonAutoImports": "off", "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}}