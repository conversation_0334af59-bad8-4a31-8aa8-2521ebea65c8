{"name": "@pdc_frontend/pdc-vulnerability-checker", "version": "0.0.30", "description": "Checks for vulnerabilities in production dependencies (use --include-dev for dev dependencies).", "packageManager": "pnpm@8.15.6", "bin": {"check-licenses": "./dist/check-vulnerabilities.js"}, "exports": {".": "./src/index.ts"}, "scripts": {"init": "pnpm i && pnpm run build", "lint": "eslint bin/**/*.ts", "build": "pnpm format && pnpm lint && pnpm tsc", "lint:fix": "eslint bin/**/*.ts --fix", "format": "prettier --write bin/**/*.ts", "format:check": "prettier --check bin/**/*.ts", "check": "pnpm build && node dist/check-vulnerabilities.js", "check:all": "pnpm build && node dist/check-vulnerabilities.js --include-dev", "release": "pnpm i && pnpm run build && pnpm publish --dry-run --no-git-checks && pnpm publish --no-git-checks", "bump:patch": "npm version patch", "bump:minor": "npm version minor", "bump:major": "npm version major", "publify": "pnpm bump:patch && pnpm release"}, "files": ["dist"], "devDependencies": {"@eslint/js": "^9.31.0", "@types/node": "^24.0.3", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "eslint": "^9.31.0", "eslint-config-prettier": "^10.1.8", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.32.0", "eslint-plugin-n": "^17.21.0", "eslint-plugin-promise": "^7.2.1", "prettier": "^3.6.2", "types-package-json": "^2.0.39", "typescript": "^5.4.0"}, "publishConfig": {"registry": "https://svrtfs1/PDC/pdcTools/_packaging/pdc_frontend/npm/registry/"}, "dependencies": {"chalk": "4.1.2", "cli-table3": "^0.6.5", "zod": "^4.0.8"}}