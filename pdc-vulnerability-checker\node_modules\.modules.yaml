hoistPattern:
  - '*'
hoistedDependencies:
  /@colors/colors/1.5.0:
    '@colors/colors': private
  /@eslint-community/eslint-utils/4.7.0(eslint@9.31.0):
    '@eslint-community/eslint-utils': public
  /@eslint-community/regexpp/4.12.1:
    '@eslint-community/regexpp': public
  /@eslint/config-array/0.21.0:
    '@eslint/config-array': public
  /@eslint/config-helpers/0.3.0:
    '@eslint/config-helpers': public
  /@eslint/core/0.15.1:
    '@eslint/core': public
  /@eslint/eslintrc/3.3.1:
    '@eslint/eslintrc': public
  /@eslint/object-schema/2.1.6:
    '@eslint/object-schema': public
  /@eslint/plugin-kit/0.3.4:
    '@eslint/plugin-kit': public
  /@humanfs/core/0.19.1:
    '@humanfs/core': private
  /@humanfs/node/0.16.6:
    '@humanfs/node': private
  /@humanwhocodes/module-importer/1.0.1:
    '@humanwhocodes/module-importer': private
  /@humanwhocodes/retry/0.4.3:
    '@humanwhocodes/retry': private
  /@nodelib/fs.scandir/2.1.5:
    '@nodelib/fs.scandir': private
  /@nodelib/fs.stat/2.0.5:
    '@nodelib/fs.stat': private
  /@nodelib/fs.walk/1.2.8:
    '@nodelib/fs.walk': private
  /@rtsao/scc/1.1.0:
    '@rtsao/scc': private
  /@types/estree/1.0.8:
    '@types/estree': private
  /@types/json-schema/7.0.15:
    '@types/json-schema': private
  /@types/json5/0.0.29:
    '@types/json5': private
  /@typescript-eslint/project-service/8.38.0(typescript@5.8.3):
    '@typescript-eslint/project-service': public
  /@typescript-eslint/scope-manager/8.38.0:
    '@typescript-eslint/scope-manager': public
  /@typescript-eslint/tsconfig-utils/8.38.0(typescript@5.8.3):
    '@typescript-eslint/tsconfig-utils': public
  /@typescript-eslint/type-utils/8.38.0(eslint@9.31.0)(typescript@5.8.3):
    '@typescript-eslint/type-utils': public
  /@typescript-eslint/types/8.38.0:
    '@typescript-eslint/types': public
  /@typescript-eslint/typescript-estree/8.38.0(typescript@5.8.3):
    '@typescript-eslint/typescript-estree': public
  /@typescript-eslint/utils/8.38.0(eslint@9.31.0)(typescript@5.8.3):
    '@typescript-eslint/utils': public
  /@typescript-eslint/visitor-keys/8.38.0:
    '@typescript-eslint/visitor-keys': public
  /acorn-jsx/5.3.2(acorn@8.15.0):
    acorn-jsx: private
  /acorn/8.15.0:
    acorn: private
  /ajv/6.12.6:
    ajv: private
  /ansi-regex/5.0.1:
    ansi-regex: private
  /ansi-styles/4.3.0:
    ansi-styles: private
  /argparse/2.0.1:
    argparse: private
  /array-buffer-byte-length/1.0.2:
    array-buffer-byte-length: private
  /array-includes/3.1.9:
    array-includes: private
  /array.prototype.findlastindex/1.2.6:
    array.prototype.findlastindex: private
  /array.prototype.flat/1.3.3:
    array.prototype.flat: private
  /array.prototype.flatmap/1.3.3:
    array.prototype.flatmap: private
  /arraybuffer.prototype.slice/1.0.4:
    arraybuffer.prototype.slice: private
  /async-function/1.0.0:
    async-function: private
  /available-typed-arrays/1.0.7:
    available-typed-arrays: private
  /balanced-match/1.0.2:
    balanced-match: private
  /brace-expansion/1.1.12:
    brace-expansion: private
  /braces/3.0.3:
    braces: private
  /call-bind-apply-helpers/1.0.2:
    call-bind-apply-helpers: private
  /call-bind/1.0.8:
    call-bind: private
  /call-bound/1.0.4:
    call-bound: private
  /callsites/3.1.0:
    callsites: private
  /color-convert/2.0.1:
    color-convert: private
  /color-name/1.1.4:
    color-name: private
  /concat-map/0.0.1:
    concat-map: private
  /cross-spawn/7.0.6:
    cross-spawn: private
  /data-view-buffer/1.0.2:
    data-view-buffer: private
  /data-view-byte-length/1.0.2:
    data-view-byte-length: private
  /data-view-byte-offset/1.0.1:
    data-view-byte-offset: private
  /debug/4.4.1:
    debug: private
  /deep-is/0.1.4:
    deep-is: private
  /define-data-property/1.1.4:
    define-data-property: private
  /define-properties/1.2.1:
    define-properties: private
  /doctrine/2.1.0:
    doctrine: private
  /dunder-proto/1.0.1:
    dunder-proto: private
  /emoji-regex/8.0.0:
    emoji-regex: private
  /enhanced-resolve/5.18.2:
    enhanced-resolve: private
  /es-abstract/1.24.0:
    es-abstract: private
  /es-define-property/1.0.1:
    es-define-property: private
  /es-errors/1.3.0:
    es-errors: private
  /es-object-atoms/1.1.1:
    es-object-atoms: private
  /es-set-tostringtag/2.1.0:
    es-set-tostringtag: private
  /es-shim-unscopables/1.1.0:
    es-shim-unscopables: private
  /es-to-primitive/1.3.0:
    es-to-primitive: private
  /escape-string-regexp/4.0.0:
    escape-string-regexp: private
  /eslint-compat-utils/0.5.1(eslint@9.31.0):
    eslint-compat-utils: public
  /eslint-import-resolver-node/0.3.9:
    eslint-import-resolver-node: public
  /eslint-module-utils/2.12.1(@typescript-eslint/parser@8.38.0)(eslint-import-resolver-node@0.3.9)(eslint@9.31.0):
    eslint-module-utils: public
  /eslint-plugin-es-x/7.8.0(eslint@9.31.0):
    eslint-plugin-es-x: public
  /eslint-scope/8.4.0:
    eslint-scope: public
  /eslint-visitor-keys/4.2.1:
    eslint-visitor-keys: public
  /espree/10.4.0:
    espree: private
  /esquery/1.6.0:
    esquery: private
  /esrecurse/4.3.0:
    esrecurse: private
  /estraverse/5.3.0:
    estraverse: private
  /esutils/2.0.3:
    esutils: private
  /fast-deep-equal/3.1.3:
    fast-deep-equal: private
  /fast-glob/3.3.3:
    fast-glob: private
  /fast-json-stable-stringify/2.1.0:
    fast-json-stable-stringify: private
  /fast-levenshtein/2.0.6:
    fast-levenshtein: private
  /fastq/1.19.1:
    fastq: private
  /file-entry-cache/8.0.0:
    file-entry-cache: private
  /fill-range/7.1.1:
    fill-range: private
  /find-up/5.0.0:
    find-up: private
  /flat-cache/4.0.1:
    flat-cache: private
  /flatted/3.3.3:
    flatted: private
  /for-each/0.3.5:
    for-each: private
  /function-bind/1.1.2:
    function-bind: private
  /function.prototype.name/1.1.8:
    function.prototype.name: private
  /functions-have-names/1.2.3:
    functions-have-names: private
  /get-intrinsic/1.3.0:
    get-intrinsic: private
  /get-proto/1.0.1:
    get-proto: private
  /get-symbol-description/1.1.0:
    get-symbol-description: private
  /get-tsconfig/4.10.1:
    get-tsconfig: private
  /glob-parent/6.0.2:
    glob-parent: private
  /globals/15.15.0:
    globals: private
  /globalthis/1.0.4:
    globalthis: private
  /gopd/1.2.0:
    gopd: private
  /graceful-fs/4.2.11:
    graceful-fs: private
  /graphemer/1.4.0:
    graphemer: private
  /has-bigints/1.1.0:
    has-bigints: private
  /has-flag/4.0.0:
    has-flag: private
  /has-property-descriptors/1.0.2:
    has-property-descriptors: private
  /has-proto/1.2.0:
    has-proto: private
  /has-symbols/1.1.0:
    has-symbols: private
  /has-tostringtag/1.0.2:
    has-tostringtag: private
  /hasown/2.0.2:
    hasown: private
  /ignore/7.0.5:
    ignore: private
  /import-fresh/3.3.1:
    import-fresh: private
  /imurmurhash/0.1.4:
    imurmurhash: private
  /internal-slot/1.1.0:
    internal-slot: private
  /is-array-buffer/3.0.5:
    is-array-buffer: private
  /is-async-function/2.1.1:
    is-async-function: private
  /is-bigint/1.1.0:
    is-bigint: private
  /is-boolean-object/1.2.2:
    is-boolean-object: private
  /is-callable/1.2.7:
    is-callable: private
  /is-core-module/2.16.1:
    is-core-module: private
  /is-data-view/1.0.2:
    is-data-view: private
  /is-date-object/1.1.0:
    is-date-object: private
  /is-extglob/2.1.1:
    is-extglob: private
  /is-finalizationregistry/1.1.1:
    is-finalizationregistry: private
  /is-fullwidth-code-point/3.0.0:
    is-fullwidth-code-point: private
  /is-generator-function/1.1.0:
    is-generator-function: private
  /is-glob/4.0.3:
    is-glob: private
  /is-map/2.0.3:
    is-map: private
  /is-negative-zero/2.0.3:
    is-negative-zero: private
  /is-number-object/1.1.1:
    is-number-object: private
  /is-number/7.0.0:
    is-number: private
  /is-regex/1.2.1:
    is-regex: private
  /is-set/2.0.3:
    is-set: private
  /is-shared-array-buffer/1.0.4:
    is-shared-array-buffer: private
  /is-string/1.1.1:
    is-string: private
  /is-symbol/1.1.1:
    is-symbol: private
  /is-typed-array/1.1.15:
    is-typed-array: private
  /is-weakmap/2.0.2:
    is-weakmap: private
  /is-weakref/1.1.1:
    is-weakref: private
  /is-weakset/2.0.4:
    is-weakset: private
  /isarray/2.0.5:
    isarray: private
  /isexe/2.0.0:
    isexe: private
  /js-yaml/4.1.0:
    js-yaml: private
  /json-buffer/3.0.1:
    json-buffer: private
  /json-schema-traverse/0.4.1:
    json-schema-traverse: private
  /json-stable-stringify-without-jsonify/1.0.1:
    json-stable-stringify-without-jsonify: private
  /json5/1.0.2:
    json5: private
  /keyv/4.5.4:
    keyv: private
  /levn/0.4.1:
    levn: private
  /locate-path/6.0.0:
    locate-path: private
  /lodash.merge/4.6.2:
    lodash.merge: private
  /math-intrinsics/1.1.0:
    math-intrinsics: private
  /merge2/1.4.1:
    merge2: private
  /micromatch/4.0.8:
    micromatch: private
  /minimatch/3.1.2:
    minimatch: private
  /minimist/1.2.8:
    minimist: private
  /ms/2.1.3:
    ms: private
  /natural-compare/1.4.0:
    natural-compare: private
  /object-inspect/1.13.4:
    object-inspect: private
  /object-keys/1.1.1:
    object-keys: private
  /object.assign/4.1.7:
    object.assign: private
  /object.fromentries/2.0.8:
    object.fromentries: private
  /object.groupby/1.0.3:
    object.groupby: private
  /object.values/1.2.1:
    object.values: private
  /optionator/0.9.4:
    optionator: private
  /own-keys/1.0.1:
    own-keys: private
  /p-limit/3.1.0:
    p-limit: private
  /p-locate/5.0.0:
    p-locate: private
  /parent-module/1.0.1:
    parent-module: private
  /path-exists/4.0.0:
    path-exists: private
  /path-key/3.1.1:
    path-key: private
  /path-parse/1.0.7:
    path-parse: private
  /picomatch/4.0.3:
    picomatch: private
  /possible-typed-array-names/1.1.0:
    possible-typed-array-names: private
  /prelude-ls/1.2.1:
    prelude-ls: private
  /punycode/2.3.1:
    punycode: private
  /queue-microtask/1.2.3:
    queue-microtask: private
  /reflect.getprototypeof/1.0.10:
    reflect.getprototypeof: private
  /regexp.prototype.flags/1.5.4:
    regexp.prototype.flags: private
  /resolve-from/4.0.0:
    resolve-from: private
  /resolve-pkg-maps/1.0.0:
    resolve-pkg-maps: private
  /resolve/1.22.10:
    resolve: private
  /reusify/1.1.0:
    reusify: private
  /run-parallel/1.2.0:
    run-parallel: private
  /safe-array-concat/1.1.3:
    safe-array-concat: private
  /safe-push-apply/1.0.0:
    safe-push-apply: private
  /safe-regex-test/1.1.0:
    safe-regex-test: private
  /semver/6.3.1:
    semver: private
  /set-function-length/1.2.2:
    set-function-length: private
  /set-function-name/2.0.2:
    set-function-name: private
  /set-proto/1.0.0:
    set-proto: private
  /shebang-command/2.0.0:
    shebang-command: private
  /shebang-regex/3.0.0:
    shebang-regex: private
  /side-channel-list/1.0.0:
    side-channel-list: private
  /side-channel-map/1.0.1:
    side-channel-map: private
  /side-channel-weakmap/1.0.2:
    side-channel-weakmap: private
  /side-channel/1.1.0:
    side-channel: private
  /stop-iteration-iterator/1.1.0:
    stop-iteration-iterator: private
  /string-width/4.2.3:
    string-width: private
  /string.prototype.trim/1.2.10:
    string.prototype.trim: private
  /string.prototype.trimend/1.0.9:
    string.prototype.trimend: private
  /string.prototype.trimstart/1.0.8:
    string.prototype.trimstart: private
  /strip-ansi/6.0.1:
    strip-ansi: private
  /strip-bom/3.0.0:
    strip-bom: private
  /strip-json-comments/3.1.1:
    strip-json-comments: private
  /supports-color/7.2.0:
    supports-color: private
  /supports-preserve-symlinks-flag/1.0.0:
    supports-preserve-symlinks-flag: private
  /tapable/2.2.2:
    tapable: private
  /to-regex-range/5.0.1:
    to-regex-range: private
  /ts-api-utils/2.1.0(typescript@5.8.3):
    ts-api-utils: private
  /ts-declaration-location/1.0.7(typescript@5.8.3):
    ts-declaration-location: private
  /tsconfig-paths/3.15.0:
    tsconfig-paths: private
  /type-check/0.4.0:
    type-check: private
  /typed-array-buffer/1.0.3:
    typed-array-buffer: private
  /typed-array-byte-length/1.0.3:
    typed-array-byte-length: private
  /typed-array-byte-offset/1.0.4:
    typed-array-byte-offset: private
  /typed-array-length/1.0.7:
    typed-array-length: private
  /unbox-primitive/1.1.0:
    unbox-primitive: private
  /undici-types/7.8.0:
    undici-types: private
  /uri-js/4.4.1:
    uri-js: private
  /which-boxed-primitive/1.1.1:
    which-boxed-primitive: private
  /which-builtin-type/1.2.1:
    which-builtin-type: private
  /which-collection/1.0.2:
    which-collection: private
  /which-typed-array/1.1.19:
    which-typed-array: private
  /which/2.0.2:
    which: private
  /word-wrap/1.2.5:
    word-wrap: private
  /yocto-queue/0.1.0:
    yocto-queue: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@8.15.6
pendingBuilds: []
prunedAt: Wed, 30 Jul 2025 06:57:28 GMT
publicHoistPattern:
  - '*eslint*'
  - '*prettier*'
registries:
  '@pdc_frontend': https://svrtfs1.pdc.xx/PDC/pdcTools/_packaging/pdc_frontend/npm/registry/
  default: https://registry.npmjs.org/
skipped: []
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v3
virtualStoreDir: C:\repos\pdcTools_frontend\pdc-vulnerability-checker\node_modules\.pnpm
