import chalk from "chalk";
import { PnpmAuditSeverity } from "./types";

/** Extract error message from unknown error type */
export const getErrorMsg = (error: unknown): string => {
  if (error instanceof Error) return error.message;
  try {
    return JSON.stringify(error);
  } catch {
    return String(error);
  }
};

/** Shortcut function to log an error and exit the process */
export const exitWithErrorLog = (error: Error | string | unknown): never => {
  console.error(`${getErrorMsg(error)}`);
  process.exitCode = 1;
  process.exit(1);
};

/** Parse command line arguments for allowlist path */
export const getAllowlistPath = (args: string[]): string => {
  const defaultPath = "./vulnerability-allowlist.json";
  // Check for --allowlist-path=value format
  const allowlistArg = args.find((arg) => arg.startsWith("--allowlist-path="));
  if (allowlistArg) {
    return allowlistArg.split("=")[1];
  }
  // Check for -A value format
  const aIndex = args.indexOf("-A");
  if (aIndex > -1) {
    return args[aIndex + 1];
  }
  return defaultPath;
};

export const brightRed = "#FF2400";
export const brightOrange = "#FF5E0E";
export const brightYellow = "#FFFF33";

/** Convert severity string to colored string */
export const severity2colorString = (input: PnpmAuditSeverity): string => {
  let severity: string = input;
  switch (input) {
    case "critical":
      severity = chalk.hex(brightRed)(severity);
      break;
    case "high":
      severity = chalk.hex(brightOrange)(severity);
      break;
    case "moderate":
      severity = chalk.hex(brightYellow)(severity);
      break;
    case "low":
      severity = chalk.green(severity);
      break;
    default:
      severity = chalk.gray(severity);
      break;
  }
  return severity;
};

/** Log CLI help text to console  */
export const logHelp2Console = (): void => {
  console.log("PDC Vulnerability Checker\n");
  console.log("Usage: check-vulnerabilities [options]\n");
  console.log("Options:");
  console.log("  --prod-only, -P                  Check only production dependencies for vulnerabilities");
  console.log(
    "  --allowlist-path=PATH, -A PATH   Specify path to allowlist file (default: ./vulnerability-allowlist.json)",
  );
  console.log("  --help, -h                       Show this help message");
  console.log("");
  console.log("By default, both production and development dependencies are checked for vulnerabilities.\n");
};
