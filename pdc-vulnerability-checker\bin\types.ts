import z from "zod";
import {
  AllowlistEntrySchema,
  AllowlistSchema,
  PnpmAuditAdvisorySchema,
  PnpmAuditOutputSchema,
  PnpmAuditSeveritySchema,
} from "./validation";

// TypeScript types derived from Zod schemas - Audit Output
export type PnpmAuditSeverity = z.infer<typeof PnpmAuditSeveritySchema>;
export type PnpmAuditAdvisory = z.infer<typeof PnpmAuditAdvisorySchema>;
export type PnpmAuditOutput = z.infer<typeof PnpmAuditOutputSchema>;

// TypeScript types derived from Zod schemas
export type AllowlistEntry = z.infer<typeof AllowlistEntrySchema>;
export type Allowlist = z.infer<typeof AllowlistSchema>;
