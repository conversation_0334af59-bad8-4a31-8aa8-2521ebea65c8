{"actions": [{"action": "update", "resolves": [{"id": 1105444, "path": ".>eslint-plugin-import>@typescript-eslint/parser>@typescript-eslint/typescript-estree>minimatch>brace-expansion", "dev": false, "optional": false, "bundled": false}], "module": "brace-expansion", "target": "2.0.2", "depth": 6}, {"action": "update", "resolves": [{"id": 1106487, "path": ".>eslint>@eslint/plugin-kit", "dev": false, "optional": false, "bundled": false}], "module": "@eslint/plugin-kit", "target": "0.3.3", "depth": 3}, {"action": "review", "module": "brace-expansion", "resolves": [{"id": 1105443, "path": ".>eslint>minimatch>brace-expansion", "dev": false, "optional": false, "bundled": false}]}], "advisories": {"1105443": {"findings": [{"version": "1.1.11", "paths": [".>eslint>minimatch>brace-expansion"]}], "found_by": null, "deleted": null, "references": "- https://nvd.nist.gov/vuln/detail/CVE-2025-5889\n- https://github.com/juliangruber/brace-expansion/pull/65/commits/a5b98a4f30d7813266b221435e1eaaf25a1b0ac5\n- https://gist.github.com/mmmsssttt404/37a40ce7d6e5ca604858fe30814d9466\n- https://vuldb.com/?ctiid.311660\n- https://vuldb.com/?id.311660\n- https://vuldb.com/?submit.585717\n- https://github.com/juliangruber/brace-expansion/commit/0b6a9781e18e9d2769bb2931f4856d1360243ed2\n- https://github.com/juliangruber/brace-expansion/commit/15f9b3c75ebf5988198241fecaebdc45eff28a9f\n- https://github.com/juliangruber/brace-expansion/commit/36603d5f3599a37af9e85eda30acd7d28599c36e\n- https://github.com/juliangruber/brace-expansion/commit/c3c73c8b088defc70851843be88ccc3af08e7217\n- https://github.com/advisories/GHSA-v6h2-p8h4-qcjw", "created": "2025-06-09T21:30:51.000Z", "id": 1105443, "npm_advisory_id": null, "overview": "A vulnerability was found in juliangruber brace-expansion up to 1.1.11/2.0.1/3.0.0/4.0.0. It has been rated as problematic. Affected by this issue is the function expand of the file index.js. The manipulation leads to inefficient regular expression complexity. The attack may be launched remotely. The complexity of an attack is rather high. The exploitation is known to be difficult. The exploit has been disclosed to the public and may be used. Upgrading to version 1.1.12, 2.0.2, 3.0.1 and 4.0.1 is able to address this issue. The name of the patch is `a5b98a4f30d7813266b221435e1eaaf25a1b0ac5`. It is recommended to upgrade the affected component.", "reported_by": null, "title": "brace-expansion Regular Expression Denial of Service vulnerability", "metadata": null, "cves": ["CVE-2025-5889"], "access": "public", "severity": "low", "module_name": "brace-expansion", "vulnerable_versions": ">=1.0.0 <=1.1.11", "github_advisory_id": "GHSA-v6h2-p8h4-qcjw", "recommendation": "Upgrade to version 1.1.12 or later", "patched_versions": ">=1.1.12", "updated": "2025-06-11T21:00:29.000Z", "cvss": {"score": 3.1, "vectorString": "CVSS:3.1/AV:N/AC:H/PR:L/UI:N/S:U/C:N/I:N/A:L"}, "cwe": ["CWE-400"], "url": "https://github.com/advisories/GHSA-v6h2-p8h4-qcjw"}, "1105444": {"findings": [{"version": "2.0.1", "paths": [".>eslint-plugin-import>@typescript-eslint/parser>@typescript-eslint/typescript-estree>minimatch>brace-expansion"]}], "found_by": null, "deleted": null, "references": "- https://nvd.nist.gov/vuln/detail/CVE-2025-5889\n- https://github.com/juliangruber/brace-expansion/pull/65/commits/a5b98a4f30d7813266b221435e1eaaf25a1b0ac5\n- https://gist.github.com/mmmsssttt404/37a40ce7d6e5ca604858fe30814d9466\n- https://vuldb.com/?ctiid.311660\n- https://vuldb.com/?id.311660\n- https://vuldb.com/?submit.585717\n- https://github.com/juliangruber/brace-expansion/commit/0b6a9781e18e9d2769bb2931f4856d1360243ed2\n- https://github.com/juliangruber/brace-expansion/commit/15f9b3c75ebf5988198241fecaebdc45eff28a9f\n- https://github.com/juliangruber/brace-expansion/commit/36603d5f3599a37af9e85eda30acd7d28599c36e\n- https://github.com/juliangruber/brace-expansion/commit/c3c73c8b088defc70851843be88ccc3af08e7217\n- https://github.com/advisories/GHSA-v6h2-p8h4-qcjw", "created": "2025-06-09T21:30:51.000Z", "id": 1105444, "npm_advisory_id": null, "overview": "A vulnerability was found in juliangruber brace-expansion up to 1.1.11/2.0.1/3.0.0/4.0.0. It has been rated as problematic. Affected by this issue is the function expand of the file index.js. The manipulation leads to inefficient regular expression complexity. The attack may be launched remotely. The complexity of an attack is rather high. The exploitation is known to be difficult. The exploit has been disclosed to the public and may be used. Upgrading to version 1.1.12, 2.0.2, 3.0.1 and 4.0.1 is able to address this issue. The name of the patch is `a5b98a4f30d7813266b221435e1eaaf25a1b0ac5`. It is recommended to upgrade the affected component.", "reported_by": null, "title": "brace-expansion Regular Expression Denial of Service vulnerability", "metadata": null, "cves": ["CVE-2025-5889"], "access": "public", "severity": "low", "module_name": "brace-expansion", "vulnerable_versions": ">=2.0.0 <=2.0.1", "github_advisory_id": "GHSA-v6h2-p8h4-qcjw", "recommendation": "Upgrade to version 2.0.2 or later", "patched_versions": ">=2.0.2", "updated": "2025-06-11T21:00:29.000Z", "cvss": {"score": 3.1, "vectorString": "CVSS:3.1/AV:N/AC:H/PR:L/UI:N/S:U/C:N/I:N/A:L"}, "cwe": ["CWE-400"], "url": "https://github.com/advisories/GHSA-v6h2-p8h4-qcjw"}, "1106487": {"findings": [{"version": "0.3.1", "paths": [".>eslint>@eslint/plugin-kit"]}], "found_by": null, "deleted": null, "references": "- https://github.com/eslint/rewrite/security/advisories/GHSA-xffm-g5w8-qvg7\n- https://github.com/eslint/rewrite/commit/b283f64099ad6c6b5043387c091691d21b387805\n- https://github.com/advisories/GHSA-xffm-g5w8-qvg7", "created": "2025-07-18T20:39:12.000Z", "id": 1106487, "npm_advisory_id": null, "overview": "### Summary\n\nThe `ConfigCommentParser#parseJSONLikeConfig` API is vulnerable to a Regular Expression Denial of Service (ReDoS) attack in its only argument.\n\n### Details\n\nThe regular expression at [packages/plugin-kit/src/config-comment-parser.js:158](https://github.com/eslint/rewrite/blob/bd4bf23c59f0e4886df671cdebd5abaeb1e0d916/packages/plugin-kit/src/config-comment-parser.js#L158) is vulnerable to a quadratic runtime attack because the grouped expression is not anchored. This can be solved by prepending the regular expression with `[^-a-zA-Z0-9/]`.\n\n### PoC\n\n```javascript\nconst { ConfigCommentParser } = require(\"@eslint/plugin-kit\");\n\nconst str = `${\"A\".repeat(1000000)}?: 1 B: 2`;\n\nconsole.log(\"start\")\nvar parser = new ConfigCommentParser();\nconsole.log(parser.parseJSONLikeConfig(str));\nconsole.log(\"end\")\n\n// run `npm i @eslint/plugin-kit@0.3.3` and `node attack.js`\n// then the program will stuck forever with high CPU usage\n```\n\n### Impact\n\nThis is a Regular Expression Denial of Service attack which may lead to blocking execution and high CPU usage.", "reported_by": null, "title": "@eslint/plugin-kit is vulnerable to Regular Expression Denial of Service attacks through ConfigCommentParser", "metadata": null, "cves": [], "access": "public", "severity": "high", "module_name": "@eslint/plugin-kit", "vulnerable_versions": "<0.3.3", "github_advisory_id": "GHSA-xffm-g5w8-qvg7", "recommendation": "Upgrade to version 0.3.3 or later", "patched_versions": ">=0.3.3", "updated": "2025-07-18T20:39:13.000Z", "cvss": {"score": 0, "vectorString": null}, "cwe": ["CWE-1333"], "url": "https://github.com/advisories/GHSA-xffm-g5w8-qvg7"}}, "muted": [], "metadata": {"vulnerabilities": {"info": 0, "low": 2, "moderate": 0, "high": 1, "critical": 0}, "dependencies": 443, "devDependencies": 0, "optionalDependencies": 0, "totalDependencies": 443}}