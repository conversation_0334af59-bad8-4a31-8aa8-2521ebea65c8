#!/usr/bin/env node

import { existsSync, readFileSync } from "fs";
import { join, resolve } from "path";
import { execSync } from "child_process";
import parseSPDX from "spdx-expression-parse";

type LicenseEntry = {
  name: string;
  version: string;
  path: string;
  license: string;
  author?: string;
  licenseText?: string;
};
/**
 * License policy structure:
 * - NO_ACTION_RUNTIME_LICENSES: Safe to include in production apps; no runtime attribution needed.
 * - NO_ACTION_DEV_LICENSES: Safe in dev/build/test tools; not shipped to users.
 * - ACTION_REQUIRED_RUNTIME_LICENSES: Allowed in prod, but must be attributed visibly in UI.
 * - ACTION_REQUIRED_DEV_LICENSES: Rare case; attribution required even if only used in dev.
 *
 * All values should be SPDX identifiers (uppercased). Use https://spdx.org/licenses/.
 */

// ✅ Licenses allowed at runtime (dependency)
// These are permissive or public-domain licenses with no UI attribution requirement
const NO_ACTION_RUNTIME_LICENSES = new Set([
  "MIT", // Permissive; attribution required in distribution, not in UI
  "MIT-0", // Equivalent to public domain; no attribution needed
  "ISC", // MIT-like; very permissive, no UI obligation
  "APACHE-2.0", // Requires NOTICE file in redistributions, but not runtime UI credit
  "BSD", // Generic BSD reference; used by pnpm output when variant is unclear
  "BSD-2-CLAUSE", // Requires attribution in documentation/source, not runtime UI
  "BSD-3-CLAUSE", // Same as above, adds non-endorsement clause
  "0BSD", // Public domain equivalent; no attribution required
  "UNLICENSE", // Public domain dedication; no obligations
  "CC0-1.0", // Creative Commons zero; unrestricted use
  "ZLIB", // Minimal attribution in docs/source; nothing in UI
  "PYTHON-2.0", // Permissive license used by Python; attribution only if redistributing Python itself
  "ARTISTIC-2.0", // Used by Perl; permits closed redistribution without runtime attribution
  "BLUEOAK-1.0.0", // Explicitly states no runtime or UI attribution required
  "WTFPL", // “Do What You Want” license; no restrictions at all
  "AFL-2.1", // Academic Free License; only requires attribution in redistributions
  "MPL-2.0", // File-level copyleft; no UI requirement if you don't modify covered files
  "PUBLIC DOMAIN", // Catch-all for explicitly declared public domain content
]);

// ✅ Licenses allowed only in devDependencies (build scripts/tools)
// These are acceptable for development use, even if they’d be restricted at runtime
const NO_ACTION_DEV_LICENSES = new Set([
  ...NO_ACTION_RUNTIME_LICENSES, // Inherit everything that's already OK for runtime
  "CC-BY-4.0", // Creative Commons Attribution 4.0; acceptable in dev tools, but requires attribution if used in production
  "CC-BY-3.0", // Same as above; allows use in dev tools but requires attribution if used in production
]);

// ⚠️ Licenses requiring visible attribution in the app if used at runtime
// These licenses impose runtime UI obligations if redistributed to users
const ACTION_REQUIRED_RUNTIME_LICENSES = new Set([
  "CC-BY-3.0", // Requires credit to author in a prominent location (e.g., About screen)
  "CC-BY-4.0", // Same as above; more flexible in placement but still visible attribution
]);

// ⚠️ Dev-only licenses that require some form of attribution or legal action
// Leave empty unless you use tools that bundle assets needing visible credit
const ACTION_REQUIRED_DEV_LICENSES = new Set<string>([
  // Example: "PROPRIETARY-SDK" if your tooling bundles brand assets with legal obligations
]);

function normalize(license: string): string {
  return license.trim().toUpperCase().replace(/[()]/g, "").replace(/\s+/g, " ");
}

function loadWhitelist(): Set<string> {
  const file = join(process.cwd(), "license-whitelist.json");
  if (!existsSync(file)) {
    console.warn("⚠️ No license-whitelist.json found — continuing without it.");
    return new Set();
  }
  const raw = readFileSync(file, "utf-8");
  const json = JSON.parse(raw);
  return new Set(Object.keys(json));
}

function checkTree(node: any, allowed: Set<string>): boolean {
  if (!node) return false;
  if (node.license) {
    return allowed.has(normalize(node.license));
  }
  if (node.left && node.right) {
    return checkTree(node.left, allowed) || checkTree(node.right, allowed);
  }
  return false;
}

function isAllowedBySet(expr: string, allowed: Set<string>): boolean {
  try {
    const tree = parseSPDX(expr);
    return checkTree(tree, allowed);
  } catch {
    return allowed.has(normalize(expr));
  }
}

function runLicenseCommand(folder: string, flags: string[]): LicenseEntry[] {
  const cwd = resolve(process.cwd(), folder);
  try {
    const out = execSync(`pnpm licenses list --json ${flags.join(" ")}`, {
      cwd,
      encoding: "utf-8",
    });
    const parsed = JSON.parse(out);
    if (typeof parsed === "object" && !Array.isArray(parsed)) {
      const allPackages: LicenseEntry[] = [];
      for (const packages of Object.values(parsed)) {
        if (Array.isArray(packages)) {
          allPackages.push(...packages);
        }
      }
      return allPackages;
    }
    throw new Error("Unexpected format from pnpm licenses list");
  } catch (err) {
    console.error(
      `❌ Failed to run pnpm licenses list ${flags.join(" ")} in ${cwd}`
    );
    console.error((err as Error).message);
    process.exit(1);
  }
}

function checkLicenses(folder: string, whitelist: Set<string>) {
  const resolved = resolve(process.cwd(), folder);
  const nodeModules = join(resolved, "node_modules");
  if (!existsSync(nodeModules)) {
    console.error(
      `❌ node_modules missing in ${resolved}. Please run 'pnpm install' first.`
    );
    process.exit(1);
  }

  console.log(`📦 Checking licenses in: ${resolved}`);

  const prodPackages = runLicenseCommand(folder, ["--prod"]);
  const devPackages = runLicenseCommand(folder, ["--dev"]);

  const safe: string[] = [];
  const needsAction: string[] = [];
  const seen = new Set<string>();

  function classify(pkg: LicenseEntry, isDev: boolean) {
    const key = `${pkg.name}@${pkg.version}`;
    if (seen.has(key)) return;
    seen.add(key);

    const license = pkg.license || "UNKNOWN";

    const isWhitelisted =
      whitelist.has(key) || // Exact match: name@version
      whitelist.has(pkg.name); // Broad match: name

    // ✅ No license
    if (license === "UNKNOWN") {
      if (!isWhitelisted) needsAction.push(`${key}: UNKNOWN`);
      return;
    }

    // ✅ Runtime logic
    if (!isDev) {
      if (isAllowedBySet(license, NO_ACTION_RUNTIME_LICENSES)) {
        safe.push(key);
        return;
      }
      if (isAllowedBySet(license, ACTION_REQUIRED_RUNTIME_LICENSES)) {
        if (!isWhitelisted) needsAction.push(`${key} (runtime): ${license}`);
        return;
      }
    }

    // ✅ Dev-only logic
    if (isDev) {
      if (isAllowedBySet(license, NO_ACTION_DEV_LICENSES)) {
        safe.push(key);
        return;
      }
      if (isAllowedBySet(license, ACTION_REQUIRED_DEV_LICENSES)) {
        if (!isWhitelisted) needsAction.push(`${key} (dev): ${license}`);
        return;
      }
    }

    // ❌ Fallback: unknown/unclassified
    if (!isWhitelisted) needsAction.push(`${key}: ${license}`);
  }

  prodPackages.forEach((pkg) => classify(pkg, false));
  devPackages.forEach((pkg) => classify(pkg, true));

  console.log(`\n✅ ${safe.length} packages passed license checks.`);

  if (needsAction.length > 0) {
    console.error(
      `\n❌ ${needsAction.length} packages need attribution or review:`
    );
    for (const line of needsAction) {
      console.error(`  - ${line}`);
    }
    process.exit(1);
  }

  console.log("\n✅ All license checks passed.");
}

function main() {
  const args = process.argv.slice(2);
  if (args.length === 0) {
    console.error("❌ Please provide at least one folder to check.");
    console.error("Usage: check-licenses <folder1> [folder2 ...]");
    process.exit(1);
  }

  const whitelist = loadWhitelist();

  for (const folder of args) {
    console.log("\n========================================");
    checkLicenses(folder, whitelist);
  }
}

main();
