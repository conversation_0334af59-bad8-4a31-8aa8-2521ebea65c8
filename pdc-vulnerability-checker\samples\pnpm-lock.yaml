lockfileVersion: '6.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

dependencies:
  lodash:
    specifier: 4.17.15
    version: 4.17.15

devDependencies:
  axios:
    specifier: 0.21.0
    version: 0.21.0
  express-fileupload:
    specifier: 1.1.8
    version: 1.1.8
  moment:
    specifier: 2.29.0
    version: 2.29.0

packages:

  /axios@0.21.0:
    resolution: {integrity: sha512-fmkJBknJKoZwem3/IKSSLpkdNXZeBu5Q7GA/aRsr2btgrptmSCxi2oFjZHqGdK9DoTil9PIHlPIZw2EcRJXRvw==}
    deprecated: Critical security vulnerability fixed in v0.21.1. For more information, see https://github.com/axios/axios/pull/3410
    dependencies:
      follow-redirects: 1.15.9
    transitivePeerDependencies:
      - debug
    dev: true

  /busboy@0.3.1:
    resolution: {integrity: sha512-y7tTxhGKXcyBxRKAni+awqx8uqaJKrSFSNFSeRG5CsWNdmy2BIK+6VGWEW7TZnIO/533mtMEA4rOevQV815YJw==}
    engines: {node: '>=4.5.0'}
    dependencies:
      dicer: 0.3.0
    dev: true

  /dicer@0.3.0:
    resolution: {integrity: sha512-MdceRRWqltEG2dZqO769g27N/3PXfcKl04VhYnBlo2YhH7zPi88VebsjTKclaOyiuMaGU72hTfw3VkUitGcVCA==}
    engines: {node: '>=4.5.0'}
    dependencies:
      streamsearch: 0.1.2
    dev: true

  /express-fileupload@1.1.8:
    resolution: {integrity: sha512-5FY1of8hxuI6HfmceDKc2Y6vg3EuXAlLz0Jw/BUglzrm7nKRuvjN6Y8ZgBfRp+1t5YkGAX7TEb5a5AmG5RqDcA==}
    engines: {node: '>=8.0.0'}
    deprecated: Please upgrade express-fileupload to version 1.1.10+ due to a security vulnerability with the parseNested option
    dependencies:
      busboy: 0.3.1
    dev: true

  /follow-redirects@1.15.9:
    resolution: {integrity: sha512-gew4GsXizNgdoRyqmyfMHyAmXsZDk6mHkSxZFCzW9gwlbtOW44CDtYavM+y+72qD/Vq2l550kMF52DT8fOLJqQ==}
    engines: {node: '>=4.0'}
    peerDependencies:
      debug: '*'
    peerDependenciesMeta:
      debug:
        optional: true
    dev: true

  /lodash@4.17.15:
    resolution: {integrity: sha512-8xOcRHvCjnocdS5cpwXQXVzmmh5e5+saE2QGoeQmbKmRS6J3VQppPOIt0MnmE+4xlZoumy0GPG0D0MVIQbNA1A==}
    dev: false

  /moment@2.29.0:
    resolution: {integrity: sha512-z6IJ5HXYiuxvFTI6eiQ9dm77uE0gyy1yXNApVHqTcnIKfY9tIwEjlzsZ6u1LQXvVgKeTnv9Xm7NDvJ7lso3MtA==}
    dev: true

  /streamsearch@0.1.2:
    resolution: {integrity: sha512-jos8u++JKm0ARcSUTAZXOVC0mSox7Bhn6sBgty73P1f3JGf7yG2clTbBNHUdde/kdvP2FESam+vM6l8jBrNxHA==}
    engines: {node: '>=0.8.0'}
    dev: true
