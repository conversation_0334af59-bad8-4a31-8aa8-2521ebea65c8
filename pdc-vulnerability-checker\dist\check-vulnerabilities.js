"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const chalk_1 = __importDefault(require("chalk"));
const child_process_1 = require("child_process");
const cli_table3_1 = __importDefault(require("cli-table3"));
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const utils_1 = require("./utils");
const validation_1 = require("./validation");
// Parse command line arguments
const args = process.argv.slice(2);
// Show help if requested
if (args.includes("--help") || args.includes("-h")) {
    (0, utils_1.logHelp2Console)();
    process.exit(0);
}
const prodOnly = args.includes("--prod-only") || args.includes("-P");
// Parse allowlist path argument
const getAllowlistPath = () => {
    const defaultPath = "./vulnerability-allowlist.json";
    // Check for --allowlist-path=value format
    const allowlistArg = args.find((arg) => arg.startsWith("--allowlist-path="));
    if (allowlistArg) {
        return allowlistArg.split("=")[1];
    }
    // Check for -A=value format
    const shortArg = args.find((arg) => arg.startsWith("-A="));
    if (shortArg) {
        return shortArg.split("=")[1];
    }
    return defaultPath;
};
// Constants
const scriptDir = path_1.default.dirname(__filename);
const AUDIT_COMMAND = prodOnly ? "pnpm audit --json --prod" : "pnpm audit --json";
console.log(`AUDIT_COMMAND`, AUDIT_COMMAND);
const ALLOWLIST_PATH = getAllowlistPath();
/** Log package name and version from package.json */
const logPackageInfo = () => {
    try {
        // Get the directory where this script is located, then go up one level to find package.json
        const packageJsonPath = path_1.default.join(scriptDir, "..", "package.json");
        const packageContent = fs_1.default.readFileSync(packageJsonPath, "utf-8");
        const packageData = JSON.parse(packageContent);
        console.log(`📦 Package: ${packageData.name}, version: ${packageData.version}\n`);
    }
    catch (error) {
        (0, utils_1.exitWithErrorLog)(`❌ Could not load package.json: ${(0, utils_1.getErrorMsg)(error)}`);
    }
};
/** Load the allowlist from file. Return the content as string; if no file, return null. */
const loadAllowlist = () => {
    try {
        const content = fs_1.default.readFileSync(ALLOWLIST_PATH, "utf-8");
        console.log(`Allowlist read from: ${ALLOWLIST_PATH}\n`);
        return content;
    }
    catch (error) {
        // If the file exists but reading it fails, exit the script:
        if (error && typeof error === "object" && "code" in error && error.code !== "ENOENT") {
            (0, utils_1.exitWithErrorLog)(`❌ ${(0, utils_1.getErrorMsg)(error)}`);
        }
        // If the file doesn't exist, it's not an error, just continue without it:
        console.log(chalk_1.default.hex(utils_1.brightYellow)("⚠️ No allowlist file found. Continuing without allowlist.\n"));
    }
    return null;
};
/** Parse and validate the allowlist file content. */
const checkAllowlistFile = (fileContent) => {
    try {
        return (0, validation_1.parseAndValidateAllowlist)(fileContent);
    }
    catch (error) {
        (0, utils_1.exitWithErrorLog)(`❌ ${(0, utils_1.getErrorMsg)(error)}`);
        // TypeScript doesn't understand that exitWithErrorLog never returns:
        throw new Error("unreachable");
    }
};
/** Run pnpm audit command, validate and parse the result */
const runAudit = () => {
    try {
        const raw = (0, child_process_1.execSync)(AUDIT_COMMAND, { encoding: "utf-8" });
        const output = (0, validation_1.parseAndValidateAuditOutput)(raw);
        return output;
    }
    catch (error) {
        // 'pnpm audit' returns non-zero exit code when vulnerabilities are found
        // but still outputs valid JSON to stdout.
        // In that case, we parse the error.stdout and return that:
        if (error && typeof error === "object" && "stdout" in error && typeof error.stdout === "string") {
            try {
                const output = (0, validation_1.parseAndValidateAuditOutput)(error.stdout);
                return output;
            }
            catch (validationError) {
                (0, utils_1.exitWithErrorLog)(`❌ ${(0, utils_1.getErrorMsg)(validationError)}`);
            }
        }
        (0, utils_1.exitWithErrorLog)(`❌ ${(0, utils_1.getErrorMsg)(error)}`);
        // TypeScript doesn't understand that exitWithErrorLog never returns:
        throw new Error("unreachable");
    }
};
// Main audit check function
const checkVulnerabilities = () => {
    logPackageInfo();
    // Log what dependencies are being checked
    if (prodOnly) {
        console.log("🔍 Checking vulnerabilities in production dependencies only");
        console.log("   Use default (no flags) to include all dependencies\n");
    }
    else {
        console.log("🔍 Checking vulnerabilities in all dependencies\n");
    }
    /**
     * Three options for the allowlist file:
     * 1. It doesn't exist -> log that to the console and go on without the allowlist.
     * 2. It's invalid -> log that to the console and exit the script.
     * 3. It's valid -> return it and go on using the allowlist.
     */
    const allowlistFile = loadAllowlist();
    const allowlist = allowlistFile ? checkAllowlistFile(allowlistFile) : {};
    // Run the audit and parse the output (with Zod validation)
    const audit = runAudit();
    // Extract all advisory objects from the audit report
    const allFindings = Object.values(audit.advisories);
    // Filter for only critical or high severity issues
    const severeFindings = allFindings.filter((advisory) => advisory.severity === "critical" || advisory.severity === "high");
    // Further filter to only those not explicitly allowed in the allowlist
    // Use 'github_advisory_id' as the key for allowlist matching
    const unapproved = severeFindings.filter((adv) => !allowlist[adv.github_advisory_id]);
    const unApprNr = unapproved.length;
    // If unapproved issues are found, log them and mark CI failure
    if (unApprNr > 0) {
        console.log(chalk_1.default.hex(utils_1.brightRed)(`🚨 Found ${unApprNr} unapproved critical/high severity vulnerabilities:`));
        // Go through each unapproved advisory and log its details:
        unapproved.forEach((adv) => {
            const severity = (0, utils_1.severity2colorString)(adv.severity);
            const table = new cli_table3_1.default({ colWidths: [20, 50] });
            table.push(["ID ", adv.github_advisory_id]);
            table.push(["Severity ", severity]);
            table.push(["Module ", adv.module_name]);
            table.push(["Updated ", adv.updated]);
            table.push(["Title ", adv.title]);
            table.push(["Recommendation ", adv.recommendation]);
            console.log(table.toString());
        });
        console.log(chalk_1.default.hex(utils_1.brightYellow)("📄 Tip: Add approved vulnerabilities to the allowlist with justification.\nRun 'pnpm audit' to get more details."));
        // Return 1 to indicate failure (no need to exit here):
        process.exitCode = 1;
    }
    else {
        console.log(chalk_1.default.green("✅ No unapproved critical/high severity vulnerabilities found."));
        // Return 0 to indicate success:
        process.exitCode = 0;
    }
    console.log(""); // empty line
};
// Execute the audit check
checkVulnerabilities();
