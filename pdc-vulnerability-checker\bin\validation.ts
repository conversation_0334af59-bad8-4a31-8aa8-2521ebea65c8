import { z } from "zod";
import { Allowlist, PnpmAuditOutput } from "./types";
import { getErrorMsg } from "./utils";

// Zod schema for AllowlistEntry
export const AllowlistEntrySchema = z.object({
  approvedBy: z.string().min(3, "approvedBy must not be empty"),
  reason: z.string().min(20, "reason must not be empty"),
  date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "date must be in YYYY-MM-DD format"),
});

// Zod schema for Allowlist
export const AllowlistSchema = z.record(z.string(), AllowlistEntrySchema);

// Zod schema for audit output severity levels
export const PnpmAuditSeveritySchema = z.enum(["critical", "high", "moderate", "low"]);

// Zod schema for audit advisory findings
export const PnpmAuditFindingSchema = z.object({
  version: z.string(),
  paths: z.array(z.string()),
});

// Zod schema for audit advisory
export const PnpmAuditAdvisorySchema = z.object({
  id: z.number(),
  severity: PnpmAuditSeveritySchema,
  module_name: z.string(),
  title: z.string(),
  overview: z.string(),
  updated: z.string(),
  github_advisory_id: z.string(),
  cves: z.array(z.string()),
  findings: z.array(PnpmAuditFindingSchema),
  recommendation: z.string(),
  patched_versions: z.string(),
  vulnerable_versions: z.string(),
});

// Zod schema for action resolve items
export const PnpmAuditActionResolveSchema = z.object({
  id: z.number(),
  path: z.string(),
  dev: z.boolean(),
  optional: z.boolean(),
  bundled: z.boolean(),
});

// Zod schema for audit actions
export const PnpmAuditActionSchema = z.object({
  action: z.enum(["update", "review", "install"]), // Common action types
  module: z.string(),
  resolves: z.array(PnpmAuditActionResolveSchema),
  target: z.string().optional(), // Only present for "update" actions
  depth: z.number().optional(), // Only present for "update" actions
});

// Zod schema for audit metadata vulnerabilities
export const PnpmAuditVulnerabilitiesSchema = z.object({
  info: z.number(),
  low: z.number(),
  moderate: z.number(),
  high: z.number(),
  critical: z.number(),
});

// Zod schema for audit metadata
export const PnpmAuditMetadataSchema = z.object({
  vulnerabilities: PnpmAuditVulnerabilitiesSchema,
  dependencies: z.number(),
  devDependencies: z.number(),
  optionalDependencies: z.number(),
  totalDependencies: z.number(),
});

// Zod schema for complete audit output
export const PnpmAuditOutputSchema = z.object({
  advisories: z.record(z.string(), PnpmAuditAdvisorySchema),
  actions: z.array(PnpmAuditActionSchema),
  metadata: PnpmAuditMetadataSchema,
  // Currently no sample data, so using z.unknown() for now:
  muted: z.array(z.unknown()).optional(),
});

// Type guards using Zod schemas
// export const isAllowlistEntry = (value: unknown): value is AllowlistEntry => {
//   return AllowlistEntrySchema.safeParse(value).success;
// };

// export const isAllowlist = (value: unknown): value is Allowlist => {
//   return AllowlistSchema.safeParse(value).success;
// };

// Validation functions that throw on error
// export const validateAllowlistEntry = (value: unknown): AllowlistEntry => {
//   return AllowlistEntrySchema.parse(value);
// };

export const validateAllowlist = (value: unknown): Allowlist => {
  return AllowlistSchema.parse(value);
};

// Validation function for audit output
export const validateAuditOutput = (value: unknown): PnpmAuditOutput => {
  return PnpmAuditOutputSchema.parse(value);
};

// Utility function to validate allowlist from JSON string
export const parseAndValidateAllowlist = (jsonString: string): Allowlist => {
  try {
    const parsed: unknown = JSON.parse(jsonString);
    return validateAllowlist(parsed);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessages = error.issues.map((issue) => `${issue.path.join(".")}: ${issue.message}`).join(", ");
      throw new Error(`Invalid allowlist format: ${errorMessages}`);
    }
    throw new Error(`Failed to parse allowlist JSON: ${getErrorMsg(error)}`);
  }
};

// Utility function to validate audit output from JSON string
export const parseAndValidateAuditOutput = (jsonString: string): PnpmAuditOutput => {
  try {
    const parsed: unknown = JSON.parse(jsonString);
    return validateAuditOutput(parsed);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessages = error.issues.map((issue) => `${issue.path.join(".")}: ${issue.message}`).join(", ");
      throw new Error(`Invalid audit output format: ${errorMessages}`);
    }
    throw new Error(`Failed to parse audit output JSON: ${getErrorMsg(error)}`);
  }
};
