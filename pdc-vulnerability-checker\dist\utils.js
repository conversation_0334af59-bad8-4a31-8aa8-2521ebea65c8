"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.logHelp2Console = exports.severity2colorString = exports.brightYellow = exports.brightOrange = exports.brightRed = exports.getAllowlistPath = exports.exitWithErrorLog = exports.getErrorMsg = void 0;
const chalk_1 = __importDefault(require("chalk"));
/** Extract error message from unknown error type */
const getErrorMsg = (error) => {
    if (error instanceof Error)
        return error.message;
    try {
        return JSON.stringify(error);
    }
    catch {
        return String(error);
    }
};
exports.getErrorMsg = getErrorMsg;
/** Shortcut function to log an error and exit the process */
const exitWithErrorLog = (error) => {
    console.error(`${(0, exports.getErrorMsg)(error)}`);
    process.exitCode = 1;
    process.exit(1);
};
exports.exitWithErrorLog = exitWithErrorLog;
/** Parse command line arguments for allowlist path */
const getAllowlistPath = (args) => {
    const defaultPath = "./vulnerability-allowlist.json";
    // Check for --allowlist-path=value format
    const allowlistArg = args.find((arg) => arg.startsWith("--allowlist-path="));
    if (allowlistArg) {
        return allowlistArg.split("=")[1];
    }
    // Check for -A value format
    const aIndex = args.indexOf("-A");
    if (aIndex > -1) {
        return args[aIndex + 1];
    }
    return defaultPath;
};
exports.getAllowlistPath = getAllowlistPath;
exports.brightRed = "#FF2400";
exports.brightOrange = "#FF5E0E";
exports.brightYellow = "#FFFF33";
/** Convert severity string to colored string */
const severity2colorString = (input) => {
    let severity = input;
    switch (input) {
        case "critical":
            severity = chalk_1.default.hex(exports.brightRed)(severity);
            break;
        case "high":
            severity = chalk_1.default.hex(exports.brightOrange)(severity);
            break;
        case "moderate":
            severity = chalk_1.default.hex(exports.brightYellow)(severity);
            break;
        case "low":
            severity = chalk_1.default.green(severity);
            break;
        default:
            severity = chalk_1.default.gray(severity);
            break;
    }
    return severity;
};
exports.severity2colorString = severity2colorString;
/** Log CLI help text to console  */
const logHelp2Console = () => {
    console.log("PDC Vulnerability Checker\n");
    console.log("Usage: check-vulnerabilities [options]\n");
    console.log("Options:");
    console.log("  --prod-only, -P              Check only production dependencies for vulnerabilities");
    console.log("  --allowlist-path=PATH, -A PATH  Specify path to allowlist file (default: ./vulnerability-allowlist.json)");
    console.log("  --help, -h                   Show this help message");
    console.log("");
    console.log("By default, both production and development dependencies are checked for vulnerabilities.\n");
};
exports.logHelp2Console = logHelp2Console;
