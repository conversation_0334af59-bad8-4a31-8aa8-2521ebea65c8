lockfileVersion: '6.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

dependencies:
  spdx-expression-parse:
    specifier: ^4.0.0
    version: 4.0.0

devDependencies:
  '@types/node':
    specifier: ^24.0.3
    version: 24.0.3
  '@types/spdx-expression-parse':
    specifier: ^3.0.5
    version: 3.0.5
  typescript:
    specifier: ^5.4.0
    version: 5.8.3

packages:

  /@types/node@24.0.3:
    resolution: {integrity: sha1-+TWRDz7s46Oi+L6GuWuoM9wobKs=}
    dependencies:
      undici-types: 7.8.0
    dev: true

  /@types/spdx-expression-parse@3.0.5:
    resolution: {integrity: sha1-1TLkNWeIHkaLEhemHptQfjglKvc=}
    dev: true

  /spdx-exceptions@2.5.0:
    resolution: {integrity: sha1-XWB9J/yAb2bXtkp2ZlD6iQ8E7WY=}
    dev: false

  /spdx-expression-parse@4.0.0:
    resolution: {integrity: sha1-ojr58xMhFUZdrCFcCZMD5M6sV5Q=}
    dependencies:
      spdx-exceptions: 2.5.0
      spdx-license-ids: 3.0.21
    dev: false

  /spdx-license-ids@3.0.21:
    resolution: {integrity: sha1-bW6YDJ3ytvyQU0OjstcCpiOVNsM=}
    dev: false

  /typescript@5.8.3:
    resolution: {integrity: sha1-kvij5ePPSXNW9BeMNM1lp/XoRA4=}
    engines: {node: '>=14.17'}
    hasBin: true
    dev: true

  /undici-types@7.8.0:
    resolution: {integrity: sha1-3gC4W3EMVBIuRPv9kR+NcBdM0pQ=}
    dev: true
